<template>
  <div class="component-test">
    <h2>BigData 组件测试页面</h2>
    
    <!-- 测试 BigDataSelector -->
    <div class="test-section">
      <h3>1. BigDataSelector 测试</h3>
      <BigDataSelector
        :exam-type-options="examTypeOptions"
        :region-options="regionOptions"
        :specific-exam-options="specificExamOptions"
        :selected-exam-type="selectedExamType"
        :selected-region="selectedRegion"
        :project-name="projectName"
        :is-tablet="false"
        @exam-type-change="onExamTypeChange"
        @region-change="onRegionChange"
        @specific-exam-change="onSpecificExamChange"
      />
    </div>

    <!-- 测试 DataStatCard -->
    <div class="test-section">
      <h3>2. DataStatCard 测试</h3>
      <DataStatCard
        :stat-data="statCardData"
        :format-number="formatNumber"
      />
    </div>

    <!-- 测试 BigDataChart -->
    <div class="test-section">
      <h3>3. BigDataChart 测试</h3>
      <BigDataChart
        title="测试图表"
        :chart-data="chartData"
        data-key="value"
        name-key="name"
        :format-number="formatNumber"
      />
    </div>

    <!-- 测试 BigDataTable -->
    <div class="test-section">
      <h3>4. BigDataTable 测试</h3>
      <BigDataTable
        title="测试表格"
        :table-data="tableData"
        :columns="tableColumns"
        :tabs="tableTabs"
        :current-tab="currentTab"
        :show-location-selector="true"
        :location-options="locationOptions"
        :selected-location="selectedLocation"
        @tab-change="onTabChange"
        @location-change="onLocationChange"
        @cell-click="onCellClick"
      />
    </div>

    <!-- 测试 JobSearchForm -->
    <div class="test-section">
      <h3>5. JobSearchForm 测试</h3>
      <JobSearchForm
        title="测试查询表单"
        :region-columns="regionColumns"
        :region-display-text="regionDisplayText"
        :unit-options="unitOptions"
        :selected-unit-text="selectedUnitText"
        :position-input="positionInput"
        @region-confirm="onRegionConfirm"
        @unit-confirm="onUnitConfirm"
        @search="onSearch"
        @clear="onClear"
        @position-input-change="onPositionInputChange"
      />
    </div>

    <!-- 测试 SearchResultTable -->
    <div class="test-section">
      <h3>6. SearchResultTable 测试</h3>
      <SearchResultTable
        :table-data="searchResultData"
        :columns="searchResultColumns"
        :format-number="formatNumber"
        @cell-click="onSearchResultCellClick"
      />
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue"
import BigDataSelector from "@/components/BigDataSelector.vue"
import DataStatCard from "@/components/DataStatCard.vue"
import BigDataChart from "@/components/BigDataChart.vue"
import BigDataTable from "@/components/BigDataTable.vue"
import JobSearchForm from "@/components/JobSearchForm.vue"
import SearchResultTable from "@/components/SearchResultTable.vue"

// 测试数据
const examTypeOptions = ref([
  { text: "国家公务员", value: "1" },
  { text: "省考公务员", value: "2" },
  { text: "事业单位", value: "3" }
])

const regionOptions = ref([
  { text: "全国", value: "" },
  { text: "北京", value: "1" },
  { text: "上海", value: "2" },
  { text: "广东", value: "3" }
])

const specificExamOptions = ref([
  { text: "2024年国考", value: "1" },
  { text: "2024年省考", value: "2" }
])

const selectedExamType = ref("国家公务员")
const selectedRegion = ref("全国")
const projectName = ref("2024年国考")

const statCardData = ref([
  { value: 85.5, label: '最低进面分数线', colorClass: '' },
  { value: 95.2, label: '最高进面分数线', colorClass: 'cred' },
  { value: 90.1, label: '平均进面分数线', colorClass: 'corange' }
])

const chartData = ref([
  { name: "全部", value: 0 },
  { name: "北京", value: 120 },
  { name: "上海", value: 98 },
  { name: "广东", value: 156 },
  { name: "浙江", value: 87 }
])

const tableData = ref([
  { id: 1, name: "测试职位1", work_unit: "测试单位1", min_score: 85.5 },
  { id: 2, name: "测试职位2", work_unit: "测试单位2", min_score: 92.3 },
  { id: 3, name: "测试职位3", work_unit: "测试单位3", min_score: 78.9 }
])

const tableColumns = [
  { key: 'position', title: '职位 名称', dataKey: 'name', clickable: true, className: 'position-title' },
  { key: 'unit', title: '招考 单位', dataKey: 'work_unit', className: 'unit-title' },
  { key: 'score', title: '最低进 面分', dataKey: 'min_score', className: 'recruit-num' }
]

const tableTabs = [
  { key: 'desc', name: '高分TOP10' },
  { key: 'asc', name: '低分TOP10' }
]

const currentTab = ref('desc')
const locationOptions = ref([
  { text: "全部", value: "" },
  { text: "北京", value: "1" },
  { text: "上海", value: "2" }
])
const selectedLocation = ref("全部")

const regionColumns = ref([
  { text: "全部地区", value: "" },
  { text: "北京", value: "1" },
  { text: "上海", value: "2" }
])
const regionDisplayText = ref("全部地区")
const unitOptions = ref([
  { text: "全部单位", value: "" },
  { text: "测试单位1", value: "1" },
  { text: "测试单位2", value: "2" }
])
const selectedUnitText = ref("全部单位")
const positionInput = ref("")

const searchResultData = ref([
  { id: 1, name: "搜索结果1", work_unit: "单位1", min_score: 88.5 },
  { id: 2, name: "搜索结果2", work_unit: "单位2", min_score: 91.2 }
])

const searchResultColumns = [
  { key: 'position', title: '职位名称', dataKey: 'name', clickable: true, className: 'position-name' },
  { key: 'unit', title: '工作单位', dataKey: 'work_unit', className: 'unit-name' },
  { key: 'score', title: '最低进面分数', dataKey: 'min_score', className: 'score-value' }
]

// 格式化数字函数
const formatNumber = (num) => {
  if (!num && num !== 0) return "-"
  let newNum = Number(num)
  const rounded = Math.round(newNum * 10) / 10
  if (rounded % 1 === 0) {
    return rounded.toString()
  } else {
    return rounded.toFixed(1)
  }
}

// 事件处理函数
const onExamTypeChange = ({ text, value }) => {
  console.log('考试类型变化:', text, value)
  selectedExamType.value = text
}

const onRegionChange = ({ text, value }) => {
  console.log('地区变化:', text, value)
  selectedRegion.value = text
}

const onSpecificExamChange = ({ text, value }) => {
  console.log('具体考试变化:', text, value)
  projectName.value = text
}

const onTabChange = (tabKey) => {
  console.log('Tab变化:', tabKey)
  currentTab.value = tabKey
}

const onLocationChange = ({ text, value }) => {
  console.log('位置变化:', text, value)
  selectedLocation.value = text
}

const onCellClick = ({ item, columnKey }) => {
  console.log('单元格点击:', item, columnKey)
}

const onRegionConfirm = ({ text, value }) => {
  console.log('地区确认:', text, value)
  regionDisplayText.value = text
}

const onUnitConfirm = ({ text, value }) => {
  console.log('单位确认:', text, value)
  selectedUnitText.value = text
}

const onSearch = ({ positionInput }) => {
  console.log('搜索:', positionInput)
}

const onClear = () => {
  console.log('清除')
  positionInput.value = ""
}

const onPositionInputChange = (value) => {
  console.log('职位输入变化:', value)
  positionInput.value = value
}

const onSearchResultCellClick = ({ item, columnKey }) => {
  console.log('搜索结果单元格点击:', item, columnKey)
}
</script>

<style lang="scss" scoped>
.component-test {
  padding: 20px;
  background: #f5f5f5;
  min-height: 100vh;

  h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #333;
  }

  .test-section {
    background: white;
    margin-bottom: 20px;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);

    h3 {
      margin-bottom: 15px;
      color: #666;
      border-bottom: 1px solid #eee;
      padding-bottom: 10px;
    }
  }
}
</style>
