<!doctype html>
<html lang="">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" href="/favicon.ico" />
  <meta name="viewport"
    content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no" />
  <meta itemprop="image"
    content="https://appresource-1253756937.cos.ap-chengdu.myqcloud.com/skb/wv/pingtuan/share.png" />
  <title></title>
  <script src="https://statics-1253756937.cos.ap-chengdu.myqcloud.com/web_project/app_share/share.js"></script>
  <script src="https://statics-1253756937.cos.ap-chengdu.myqcloud.com/web_project/app_share/jweixin-1.6.0.js"></script>

</head>
<style>
  body {
    font-family: -apple-system, BlinkMacSystemFont, Helvetica Neue, Helvetica, Segoe UI, Arial, Roboto, PingFang SC, miui, Hiragino Sans GB, Microsoft Yahei, sans-serif;
  }

  html {
    max-width: 750px;
    margin: 0 auto;
    scroll-behavior: auto !important;
    /* 覆盖可能的平滑滚动配置 */
  }
</style>


<body>
  <div id="app"></div>
  <script type="module" src="/src/main.js"></script>
  <script
    src="https://statics-1253756937.cos.ap-chengdu.myqcloud.com/public_resources/vconsole/vconsole.min.js"></script>
  <script>


    // if (window.location.href.indexOf("localhost") < 0) {
    //   new window.VConsole();
    // }
    // 测试服和本地显示打印
    if (
      window.location.href.indexOf("test") >= 0 ||
      window.location.href.indexOf("192.168") >= 0
    ) {
      setTimeout(() => {
        new window.VConsole();
      }, 3000)
    }
    // new window.VConsole();
  </script>
</body>

</html>