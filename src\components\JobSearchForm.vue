<template>
  <div class="job-search-section">
    <div class="job-search-title">{{ title }}</div>
    <div class="search-box">
      <!-- 背景图片 -->
      <img
        :src="backgroundImage"
        class="search-bg"
        alt="搜索背景"
      />
      <!-- 白色表单区域 -->
      <div class="white-box">
        <!-- 地区选择器 -->
        <RegionSelector
          v-if="showRegionSelector"
          :level="regionConfig.level"
          :editable="regionConfig.editable"
          :title="regionConfig.title"
          :label="regionConfig.label"
          :display-text="regionConfig.displayText"
          :selected-values="regionConfig.selectedValues"
          :columns="regionConfig.columns"
          @click="onRegionClick"
          @confirm="onRegionConfirm"
          @change="onRegionChange"
        />

        <!-- 用人单位选择器 -->
        <div class="select-item" @click="onUnitClick" v-if="showUnitSelector">
          <div class="select-left">
            <div class="label">
              <span>用</span>
              <span>人</span>
              <span>单</span>
              <span>位：</span>
            </div>
            <span class="selected-text">{{ selectedUnitText }}</span>
          </div>
          <img
            class="arrow-icon"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
            alt=""
          />
        </div>

        <!-- 职位输入框 -->
        <div class="select-item input-item" v-if="showPositionInput">
          <div class="select-left">
            <div class="label">
              <span>职</span>
              <span>位：</span>
            </div>
            <input
              v-model="positionInputValue"
              type="text"
              :placeholder="positionPlaceholder"
              class="position-input"
            />
          </div>
        </div>

        <!-- 搜索按钮 -->
        <van-button type="danger" class="search-btn" @click="onSearch">
          <van-icon name="search" />
          {{ searchButtonText }}
        </van-button>

        <!-- 清除按钮 -->
        <div class="clear-btn" @click="onClear">{{ clearButtonText }}</div>
      </div>
    </div>

    <!-- 用人单位选择弹窗 -->
    <van-popup v-model:show="showUnitPopup" position="bottom">
      <van-picker
        :columns="unitOptions"
        @confirm="onUnitConfirm"
        @cancel="showUnitPopup = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed } from "vue"
import RegionSelector from "./RegionSelector.vue"

// Props
const props = defineProps({
  title: {
    type: String,
    default: "岗位查询"
  },
  backgroundImage: {
    type: String,
    default: "https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/search_box_bg.png"
  },
  showRegionSelector: {
    type: Boolean,
    default: true
  },
  showUnitSelector: {
    type: Boolean,
    default: true
  },
  showPositionInput: {
    type: Boolean,
    default: true
  },
  regionConfig: {
    type: Object,
    default: () => ({
      level: 1,
      editable: true,
      title: "请选择地区",
      label: "地区：",
      displayText: "全部地区",
      selectedValues: [],
      columns: []
    })
  },
  unitOptions: {
    type: Array,
    default: () => []
  },
  selectedUnitText: {
    type: String,
    default: "全部单位"
  },
  positionInput: {
    type: String,
    default: ""
  },
  positionPlaceholder: {
    type: String,
    default: "请输入职位名称或代码"
  },
  searchButtonText: {
    type: String,
    default: "查询"
  },
  clearButtonText: {
    type: String,
    default: "清除"
  }
})

// Emits
const emit = defineEmits([
  'regionClick',
  'regionConfirm',
  'regionChange',
  'unitClick',
  'unitConfirm',
  'search',
  'clear',
  'positionInputChange'
])

// 响应式数据
const showUnitPopup = ref(false)
const positionInputValue = ref(props.positionInput)

// 监听输入框变化
import { watch } from "vue"
watch(positionInputValue, (newValue) => {
  emit('positionInputChange', newValue)
})

// 方法
const onRegionClick = () => {
  emit('regionClick')
}

const onRegionConfirm = (result) => {
  emit('regionConfirm', result)
}

const onRegionChange = (data) => {
  emit('regionChange', data)
}

const onUnitClick = () => {
  showUnitPopup.value = true
  emit('unitClick')
}

const onUnitConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    showUnitPopup.value = false
    emit('unitConfirm', {
      text: selectedOptions[0].text,
      value: selectedOptions[0].value
    })
  }
}

const onSearch = () => {
  emit('search', {
    positionInput: positionInputValue.value
  })
}

const onClear = () => {
  positionInputValue.value = ""
  emit('clear')
}
</script>

<style lang="scss" scoped>
.job-search-section {
  padding: 0.533rem 0.427rem;
  box-sizing: border-box;
  background: #ffffff;

  .job-search-title {
    font-size: 0.427rem;
    color: #22242e;
    font-weight: bold;
    margin-bottom: 0.427rem;
  }

  .search-box {
    position: relative;
    width: 100%;
    border-radius: 0.32rem;
    padding: 0.107rem;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    z-index: 1;

    .search-bg {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      border-radius: 0.32rem;
    }

    .white-box {
      position: relative;
      z-index: 2;
      flex: 1;
      width: 100%;
      border-radius: 0.213rem;
      padding: 0.533rem;
      box-sizing: border-box;

      .select-item {
        background: #ffffff;
        border-radius: 0.213rem;
        border: 0.013rem solid #ebecf0;
        margin-bottom: 0.32rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.347rem 0.32rem 0.347rem 0.427rem;
        box-sizing: border-box;
        cursor: pointer;

        &:last-of-type {
          margin-bottom: 0;
        }

        .select-left {
          display: flex;
          align-items: center;
          color: #3c3d42;
          font-size: 0.347rem;
          flex: 1;

          .label {
            width: 1.85rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-shrink: 0;
          }

          .selected-text {
            flex: 1;
            margin-left: 0;
          }
        }

        .arrow-icon {
          width: 0.43rem;
          height: 0.43rem;
        }

        // 输入框样式
        &.input-item {
          .select-left {
            .position-input {
              flex: 1;
              font-size: 0.347rem;
              color: #3c3d42;
              background: transparent;
              border: none;
              outline: none;
              padding: 0;
              margin-left: 0;

              &::placeholder {
                color: #919499;
                font-size: 0.347rem;
              }
            }
          }
        }
      }

      .search-btn {
        margin-top: 0.21rem;
        width: 100%;
        height: 1.12rem;
        border-radius: 0.213rem;
        font-size: 0.373rem;
        font-weight: 500;

        :deep(.van-icon) {
          margin-right: 0.107rem;
        }
      }

      .clear-btn {
        width: 100%;
        text-align: center;
        margin-top: 0.427rem;
        color: #ec3e33;
        font-size: 0.373rem;
        font-weight: 500;
        cursor: pointer;
      }
    }
  }
}
</style>
