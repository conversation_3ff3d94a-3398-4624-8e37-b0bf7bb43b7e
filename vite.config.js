import { fileURLToPath, URL } from "node:url"

import { defineConfig, loadEnv } from "vite"
import vue from "@vitejs/plugin-vue"

import AutoImport from "unplugin-auto-import/vite"
import Components from "unplugin-vue-components/vite"
import { VantResolver } from "@vant/auto-import-resolver"

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), "")

  return {
    base: "/position/",
    plugins: [
      vue({
        template: {
          compilerOptions: {
            // 将微信开放标签视为自定义元素
            isCustomElement: (tag) => tag.startsWith("wx-"),
          },
        },
      }),
      AutoImport({
        resolvers: [VantResolver()],
      }),
      Components({
        resolvers: [VantResolver()],
      }),
    ],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },
    build: {
      target: "es2015", // 设置构建目标为 ES2015
      outDir: "dist/position/",
      reportCompressedSize: false, // 禁用压缩大小报告以提升构建速度
      sourcemap: false, // 构建后是否生成 source map 文件
      // emptyOutDir: true, // 构建时清空该目录
      chunkSizeWarningLimit: 1000, // 提高警告阈值到 1000kb
      assetsInlineLimit: 4096, // 单位字节（1024等于1kb） 小于此阈值的导入或引用资源将内联为 base64 编码

      // 代码分割优化
      rollupOptions: {
        output: {
          // 手动分包策略
          manualChunks: {
            // Vue 核心库
            "vue-vendor": ["vue", "vue-router", "pinia"],
            // UI 组件库
            "vant-vendor": ["vant"],
            // 工具库
            "utils-vendor": ["axios", "js-cookie"],
            // 图表库
            "echarts-vendor": ["echarts"],
          },
          // 优化文件名
          chunkFileNames: "assets/js/[name]-[hash].js",
          entryFileNames: "assets/js/[name]-[hash].js",
          assetFileNames: (assetInfo) => {
            const info = assetInfo.name.split(".")
            const ext = info[info.length - 1]
            if (/\.(mp4|webm|ogg|mp3|wav|flac|aac)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/media/[name]-[hash].${ext}`
            }
            if (/\.(png|jpe?g|gif|svg)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/images/[name]-[hash].${ext}`
            }
            if (/\.(woff2?|eot|ttf|otf)(\?.*)?$/i.test(assetInfo.name)) {
              return `assets/fonts/[name]-[hash].${ext}`
            }
            return `assets/${ext}/[name]-[hash].${ext}`
          },
        },
      },

      // 压缩优化
      minify: "esbuild",
      cssMinify: "esbuild",
    },
    // CSS配置优化
    css: {
      preprocessorOptions: {
        scss: {
          // 全局SCSS变量和混入 - 使用 @use 替代 @import
          additionalData: `
          @import "@/assets/styles/variables.scss";
          @import "@/assets/styles/mixins.scss";
        `,
          // 使用现代API
          api: "modern-compiler",
          // 静默弃用警告
          silenceDeprecations: ["legacy-js-api", "import"],
        },
      },
      // CSS 代码分割
      devSourcemap: false,
    },

    // 开发服务器配置
    server: {
      open: true,
      proxy: {
        "/proxy": {
          target: env.VITE_PROXY_TARGET,
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/proxy/, ""),
        },
      },
    },

    // 依赖预构建优化
    optimizeDeps: {
      include: [
        "vue",
        "vue-router",
        "pinia",
        "vant",
        "axios",
        "js-cookie",
        "echarts/core",
        "echarts/charts/BarChart",
        "echarts/charts/LineChart",
        "echarts/charts/PieChart",
        "echarts/components/GridComponent",
        "echarts/components/TooltipComponent",
        "echarts/components/LegendComponent",
        "echarts/renderers/CanvasRenderer",
      ],
      // 排除不需要预构建的依赖
      exclude: ["@vant/auto-import-resolver"],
    },
  }
})
