<template>
  <div class="result-area" v-if="tableData.length > 0">
    <!-- 查询结果表格区域 -->
    <div class="search-result-table">
      <div class="table-header">
        <div
          v-for="column in columns"
          :key="column.key"
          class="header-item"
          :class="`header-${column.key}`"
        >
          {{ column.title }}
        </div>
      </div>
      <div class="table-body">
        <div v-for="(item, index) in tableData" :key="index" class="table-row">
          <div
            v-for="column in columns"
            :key="column.key"
            class="table-item"
            :class="`table-${column.key}`"
          >
            <span
              :class="column.className || ''"
              @click="column.clickable ? onCellClick(item, column.key) : null"
              :style="{ cursor: column.clickable ? 'pointer' : 'default' }"
            >
              {{ formatCellValue(item[column.dataKey], column.formatter) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => [
      { 
        key: 'position', 
        title: '职位名称', 
        dataKey: 'name', 
        clickable: true, 
        className: 'position-name' 
      },
      { 
        key: 'unit', 
        title: '工作单位', 
        dataKey: 'work_unit', 
        className: 'unit-name' 
      },
      { 
        key: 'score', 
        title: '最低进面分数', 
        dataKey: 'min_score', 
        className: 'score-value',
        formatter: (value) => props.formatNumber ? props.formatNumber(value) : value
      }
    ]
  },
  formatNumber: {
    type: Function,
    default: null
  }
})

// Emits
const emit = defineEmits(['cellClick'])

// 方法
const onCellClick = (item, columnKey) => {
  emit('cellClick', { item, columnKey })
}

const formatCellValue = (value, formatter) => {
  if (formatter && typeof formatter === 'function') {
    return formatter(value)
  }
  return value || "-"
}
</script>

<style lang="scss" scoped>
.result-area {
  padding: 0 0.427rem 0.533rem 0.427rem;
  box-sizing: border-box;
  background: #ffffff;

  .search-result-table {
    background: #ffffff;
    box-sizing: border-box;

    // 表格头部
    .table-header {
      display: flex;
      align-items: stretch;
      background: #fafbfc;
      border: 0.013rem solid #ebecf0;
      border-radius: 0.107rem;
      margin-bottom: 0;

      .header-item {
        font-size: 0.32rem;
        color: #919499;
        font-weight: 500;
        text-align: center;
        padding: 0.32rem 0.213rem;
        border-right: 0.013rem solid #ebecf0;
        display: flex;
        align-items: center;
        justify-content: center;

        &:last-child {
          border-right: none;
        }

        &.header-position {
          flex: 4;
        }

        &.header-unit {
          flex: 4;
        }

        &.header-score {
          flex: 3;
        }
      }
    }

    // 表格主体
    .table-body {
      .table-row {
        display: flex;
        align-items: stretch;
        border: 0.013rem solid #ebecf0;
        border-top: none;

        &:last-child {
          border-bottom-left-radius: 0.107rem;
          border-bottom-right-radius: 0.107rem;
        }

        .table-item {
          padding: 0.427rem 0.213rem;
          border-right: 0.013rem solid #ebecf0;
          display: flex;
          align-items: center;
          justify-content: center;

          &:last-child {
            border-right: none;
          }

          &.table-position {
            flex: 4;

            .position-name {
              font-size: 0.32rem;
              color: #448aff;
              line-height: 1.4;
              word-wrap: break-word;
              cursor: pointer;
            }
          }

          &.table-unit {
            flex: 4;

            .unit-name {
              font-size: 0.347rem;
              color: #3c3d42;
              line-height: 1.4;
              word-wrap: break-word;
            }
          }

          &.table-score {
            flex: 3;

            .score-value {
              font-size: 0.32rem;
              font-weight: 500;
              color: #333333;
            }
          }
        }
      }
    }
  }
}
</style>
