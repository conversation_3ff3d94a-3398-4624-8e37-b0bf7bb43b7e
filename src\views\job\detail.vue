<template>
  <div class="job-detail">
    <!-- 顶部区域 -->
    <div class="top-area">
      <div class="title-area">
        <div class="left-text">{{ detailData.name }}</div>
        <div class="right-text" :style="{ color: detailData.apply_status?.color }">
          {{ detailData.apply_status?.text }}
        </div>
      </div>
      <div class="main-area">
        <div class="entry-item">
          <div class="title">招录人数</div>
          <div class="content">{{ detailData.need_num }}人</div>
        </div>
        <div class="entry-item">
          <div class="title">招录单位</div>
          <div class="content">{{ detailData.work_unit }}</div>
        </div>
      </div>
    </div>

    <!-- 报考数据区域 -->
    <ReportCard
      title="报考数据"
      isHaveRight="1"
      :isNeedRightAngle="true"
      :updateTime="`更新时间：${detailData.job_data_record?.updated_time}`"
      class="baokao-area"
      v-if="detailData.job_data_record && getValueNum > 0"
    >
      <div class="data-area">
        <div class="data-item" :class="{'flex-center': getValueNum == 1}" v-if="detailData.job_data_record.apply_num >0">
          <div class="content">
            <div class="num red">{{ detailData.job_data_record.apply_num }}</div>
            <div class="text">报名人数</div>
          </div>
          <div class="line"></div>
        </div>
        <div class="data-item" :class="{'flex-center': getValueNum == 1}" v-if="detailData.job_data_record.approved_num >0">
          <div class="content">
            <div class="num orange">{{ detailData.job_data_record.approved_num }}</div>
            <div class="text">过审人数</div>
          </div>
          <div class="line"></div>
        </div>
        <div class="data-item" :class="{'flex-center': getValueNum == 1}" v-if="detailData.job_data_record.pay_num >0">
          <div class="content">
            <div class="num orange">{{ detailData.job_data_record.pay_num}}</div>
            <div class="text">缴费人数</div>
          </div>
          <div class="line"></div>
        </div>
        <div class="data-item" :class="{'flex-center': getValueNum == 1}" v-if="detailData.job_data_record.min_score >0">
          <div class="content">
            <div class="num">{{ formatNumber(detailData.job_data_record.min_score)}}</div>
            <div class="text">最低进面分</div>
          </div>
          <div class="line"></div>
        </div>
      </div>
      <div class="data-source">
        <div class="list-item">
          <div class="title">数据来源：</div>
          <div class="text">{{ detailData.job_data_record?.release_source }}</div>
        </div>
      </div>
    </ReportCard>

    <!-- 历年数据区域 -->
    <ReportCard title="历年数据" :isNeedRightAngle="true" class="history-area" v-if="detailData.history_data && detailData.history_data.status == 1 && detailData.history_data.data_list.length">
      <div class="position-sub-tab-list">
        <div
          class="position-sub-tab-item"
          :class="{ active: selectedSubTabs === 0 }"
          @click="onSubTabClick(0)"
        >
          相似职位
        </div>
        <div
          class="position-sub-tab-item"
          :class="{ active: selectedSubTabs === 1 }"
          @click="onSubTabClick(1)"
        >
          所属单位
        </div>
        <div
          class="position-sub-tab-item"
          :class="{ active: selectedSubTabs === 2 }"
          @click="onSubTabClick(2)"
        >
          所属地区
        </div>
      </div>
      <div class="position-table-content">
        <!-- 表格头部 -->
        <div class="position-table-header">
          <div class="header-item" :class="`header-${index + 1}`" v-for="(item, index) in dataObj.tableList" :key="index">
            <span v-for="(col) in item">{{ col }}</span>
          </div>
        </div>

        <!-- 表格数据 -->
        <div class="position-table-body">
          <div v-for="(item, index) in dataObj.list" :key="index" class="position-table-row">
            <div class="table-item" :class="`table-${index1 + 1}`" v-for="(col, index1) in item">
              <span class="data-text" :class="{'year-text': index1 == 0}">{{ col }}</span>
            </div>
          </div>
        </div>
      </div>
      <div class="desc-text">
        <span class="gray-text">说明：</span>
        {{ dataObj.note }}
      </div>
    </ReportCard>

    <!-- 职位信息区域 -->
    <ReportCard title="职位信息" class="form-area">
      <div class="form-item" v-for="(item, index) in detailData.job_info" :key="index">
        <div class="left-text">{{ item.title }}</div>
        <div class="right-text">{{ item.value || "-" }}</div>
      </div>
    </ReportCard>

    <!-- 招考条件区域 -->
    <ReportCard title="招考条件" class="form-area">
      <div class="form-item" v-for="(item, index) in detailData.apply_condition" :key="index">
        <div class="left-text">{{ item.title }}</div>
        <div class="right-text">{{ item.value || "-" }}</div>
      </div>
    </ReportCard>

    <!-- 其它信息区域 -->
    <ReportCard
      v-if="detailData.other_info && detailData.other_info.length"
      title="其它信息"
      class="form-area"
    >
      <div class="form-item" v-for="(item, index) in detailData.other_info" :key="index">
        <div class="left-text">{{ item.title }}</div>
        <div class="right-text">{{ item.value || "-" }}</div>
      </div>
    </ReportCard>

    <!-- 底部声明 -->
    <div class="bottom-text">
      声明：本站发布的招考资讯均来源于招录官方网站，由事考帮整理编辑，如遇报考疑问请咨询招考官方，若涉及版权或错误信息，请提交反馈到本站予以更新或删除。
    </div>

    <!-- 底部预留空间，避免被固定底部栏遮挡 -->
    <div class="bottom-placeholder"></div>

    <!-- 底部固定操作栏 -->
    <div class="bottom-area fixed-bottom">
      <OpenApp class="left" :options="optionsParam">
          <div class="img-area">
            <img
              class="img"
              src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/pk_black.png"
            />
          </div>
          <div class="text">加入对比</div>
      </OpenApp>
      <OpenApp class="right" :options="optionsParam">
          <div class="btn_2">查看公告</div>
      </OpenApp>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, reactive } from "vue"
import { useRouter, useRoute } from "vue-router"
import { showToast } from "vant"
import ReportCard from "@/components/job/report-card/report-card.vue"
import { getJobDetailRequest, setFollowsRequest } from "@/api/job"
import { setShare } from "@/utils/share.js"
import OpenApp from "@/components/OpenApp.vue"

const router = useRouter()
const route = useRoute()

// 响应式数据
const selectedSubTabs = ref(0)
const detailData = ref({})
const jobId = ref(null)
const isLogin = ref(false)
const pkListIds = ref([])
const loading = ref(false)

const getValueNum = computed(() => {
  if (!detailData.value?.job_data_record) return 0;

  const fields = ['apply_num', 'approved_num', 'pay_num', 'min_score'];
  const obj = detailData.value.job_data_record;
  let count = 0;

  for (const field of fields) {
    if (obj.hasOwnProperty(field) && obj[field] > 0) {
      count++;
    }
  }
  return count;
})

const dataObj = reactive({
  list: [],
  tableList:[],
  note: '',
})

const handleData = (data,index) => {
  if(data && data.length) {
    let obj = data[index]
    dataObj.list = obj.list || []
    dataObj.note = obj.note
    dataObj.tableList = obj.column_list.map(item => splitStringByLength(item))
  }
}

const optionsParam = ref({
  unitKey: "NoticeJobNoticeDetail",
  param: {
    id: null
  }
})

// 计算属性
const currentTableData = computed(() => {
  const historyData = detailData.value.history_data
  if (!historyData) return []

  switch (selectedSubTabs.value) {
    case 1:
      return historyData.belong_unit?.list || []
    case 2:
      return historyData.belong_area?.list || []
    default:
      return historyData.similar_job?.list || []
  }
})

const currentNote = computed(() => {
  const historyData = detailData.value.history_data
  if (!historyData) return ""

  switch (selectedSubTabs.value) {
    case 1:
      return historyData.belong_unit?.note || ""
    case 2:
      return historyData.belong_area?.note || ""
    default:
      return historyData.similar_job?.note || ""
  }
})

const formatNumber = (num) => {
  let newNum = Number(num)
  // 先将数字四舍五入保留一位小数
  const rounded = Math.round(newNum * 10) / 10
  // 检查是否为整数（小数部分为0）
  if (rounded % 1 === 0) {
    return rounded.toString() // 整数直接返回字符串形式
  } else {
    return rounded.toFixed(1) // 非整数保留一位小数
  }
}

// 方法定义

// 子标签点击
const onSubTabClick = (index) => {
  selectedSubTabs.value = index
  handleData(detailData.value.history_data.data_list,index)
}


// 获取职位详情数据
const getJobDetail = async () => {
  if (!jobId.value) return

  const response = await getJobDetailRequest({ id: Number(jobId.value) })
  if (response) {
    detailData.value = response.data
    optionsParam.value = {
      unitKey: "NoticeJobNoticeDetail",
      param: {
        id: detailData.value.article_id,
        type: 'notice'
      },
    }
    if(detailData.value.history_data?.status == 1) {
      handleData(detailData.value.history_data.data_list,0)
      console.log(dataObj,'=====================')
    }

    if(response.data.share_info) {
      const shareInfo = response.data.share_info
      setShare({ title: shareInfo.title, desc: shareInfo.desc, imgUrl: shareInfo.share_image })
    }
  }
}

const splitStringByLength = (str) => {
  // 校验输入是否为字符串
  if (typeof str !== 'string') {
    console.warn('输入必须是字符串');
    return [];
  }

  const result = [];
  const length = str.length;

  // 当字符串长度小于5时，每2个字一组
  if (length < 5) {
    for (let i = 0; i < length; i += 2) {
      // 截取从i开始的2个字符
      result.push(str.substring(i, i + 2));
    }
  } else {
    // 当字符串长度大于等于5时，每3个字一组
    for (let i = 0; i < length; i += 3) {
      // 截取从i开始的3个字符
      result.push(str.substring(i, i + 3));
    }
  }

  return result;
};

// 设置关注/取消关注
const setFocus = async () => {
  if (!detailData.value.id) return

  try {
    const focus = detailData.value.is_follow === 1
    const param = {
      item_type: "job",
      item_no: [detailData.value.id],
      type: focus ? "unfollow" : "follow",
    }

    const response = await setFollowsRequest(param)

    if (response?.error?.code === 0) {
      showToast(focus ? "已取消关注" : "关注成功")
      detailData.value.is_follow = focus ? 0 : 1
    } else {
      showToast(response?.error?.msg || "操作失败")
    }
  } catch (error) {
    console.error("关注操作失败:", error)
    showToast("操作失败")
  }
}

// 跳转到职位对比页面
const goComparison = () => {
  const newPkListIds = [...pkListIds.value]
  if (!newPkListIds.includes(Number(jobId.value))) {
    newPkListIds.push(Number(jobId.value))
  }

  // 存储到本地存储
  localStorage.setItem("pkListIds", JSON.stringify(newPkListIds))
  pkListIds.value = newPkListIds

  console.log("跳转到职位对比页面")
  // router.push("/job/comparison")
}

// 跳转到公告详情页面
const goNoticeDetail = () => {
  console.log("跳转到公告详情页面")
  // router.push("/notice/detail")
}

// 获取对比列表IDs
const getPkListIds = () => {
  try {
    const stored = localStorage.getItem("pkListIds")
    pkListIds.value = stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error("获取对比列表失败:", error)
    pkListIds.value = []
  }
}

// 判断是否登录
const getIsLogin = () => {
  // 这里可以根据实际的登录状态判断逻辑来实现
  // 暂时设置为true，实际项目中应该从store或其他地方获取
  isLogin.value = true
}

// 生命周期
onMounted(() => {
  // 获取路由参数中的职位ID
  jobId.value = route.query.id
  console.log("获取到职位ID:", jobId.value)

  // 获取登录状态和对比列表
  getIsLogin()
  getPkListIds()

  // 获取职位详情数据
  getJobDetail()
})
</script>

<style lang="scss" scoped>
.job-detail {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f7f8fa;
  touch-action: pan-y;
  -webkit-user-select: none;
  user-select: none;

  .back-btn {
    position: absolute;
    top: 1.493rem; // 112rpx = 112/75 = 1.493rem
    left: 0.533rem; // 40rpx = 40/75 = 0.533rem
    width: 0.533rem; // 40rpx = 40/75 = 0.533rem
    height: 0.533rem; // 40rpx = 40/75 = 0.533rem
    z-index: 1000;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .top-area {
    background: #ffffff;
    padding: 0.32rem 0.427rem 0.533rem 0.427rem; // 24rpx 32rpx 40rpx 32rpx = 24/75 32/75 40/75 32/75
    margin-bottom: 0.21rem;

    .title-area {
      display: flex;
      justify-content: space-between;
      margin-bottom: 0.427rem; // 32rpx = 32/75

      .left-text {
        font-size: 0.48rem; // 36rpx = 36/75
        color: #22242e;
        font-weight: bold;
      }

      .right-text {
        font-size: 0.373rem; // 28rpx = 28/75
        color: #13bf80;
        font-weight: 500;
      }
    }

    .main-area {
      .entry-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.32rem; // 24rpx = 24/75

        .title {
          font-size: 0.347rem; // 26rpx = 26/75
          color: #919499;
          margin-right: 0.533rem; // 40rpx = 40/75
          width: 1.4rem;
        }

        .content {
          font-size: 0.347rem; // 26rpx = 26/75
          color: #3c3d42;
        }

        &:last-of-type {
          margin-bottom: 0;
        }
      }
    }
  }

  .baokao-area {
    :deep(.data-area) {
      width: 100%;
      height: 2.267rem; // 170rpx = 170/75
      background: rgba(247, 248, 250, 0.5);
      border-radius: 0.213rem; // 16rpx = 16/75
      display: flex;
      align-items: center;
      margin-bottom: 0.427rem; // 32rpx = 32/75

      .data-item {
        flex: 1;
        position: relative;

        .content {
          display: flex;
          flex-direction: column;
          align-items: center;

          .num {
            color: #3c3d42;
            font-family: "DINBold";
            font-weight: 500;
            font-size: 0.533rem; // 40rpx = 40/75
            margin-bottom: 0.32rem; // 24rpx = 24/75
          }

          .text {
            font-size: 0.32rem; // 24rpx = 24/75
            color: #3c3d42;
          }
        }

        .line {
          position: absolute;
          right: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 0.013rem; // 1rpx = 1/75
          height: 0.64rem; // 48rpx = 48/75
          background: #ebecf0;
        }

        &:last-of-type {
          .line {
            display: none;
          }
        }
      }
    }

    :deep(.data-source) {
      .list-item {
        display: flex;
        align-items: flex-start;

        .title {
          font-size: 0.293rem; // 22rpx = 22/75
          color: #c2c5cc;
          width: 1.9rem;
        }

        .text {
          color: #919499;
          font-size: 0.293rem; // 22rpx = 22/75
        }
      }
    }
  }

  .history-area {
    :deep(.desc-text) {
      margin-top: 0.427rem; // 32rpx = 32/75
      width: 100%;
      color: #919499;
      font-size: 0.293rem; // 22rpx = 22/75

      .gray-text {
        color: #c2c5cc;
      }
    }
  }

  .form-area {
    :deep(.form-item) {
      display: flex;
      align-items: center;
      padding: 0.32rem 0.427rem; // 24rpx 32rpx = 24/75 32/75
      box-sizing: border-box;
      border-radius: 0.107rem; // 8rpx = 8/75

      &:nth-of-type(odd) {
        background: rgba(247, 248, 250, 0.6);
      }

      .left-text {
        font-size: 0.347rem; // 26rpx = 26/75
        color: #919499;
        width: 1.44rem; // 108rpx = 108/75
        margin-right: 0.533rem; // 40rpx = 40/75
      }

      .right-text {
        flex: 1;
        color: #3c3d42;
        font-size: 0.347rem; // 26rpx = 26/75
      }
    }
  }

  .bottom-text {
    padding: 0.533rem 0.427rem; // 40rpx 32rpx = 40/75 32/75
    box-sizing: border-box;
    color: #c2c5cc;
    font-size: 0.293rem; // 22rpx = 22/75
  }

  .bottom-placeholder {
    height: 2.133rem; // 160rpx = 160/75 = 2.133rem，为固定底部栏预留空间
  }

  .bottom-area {
    width: 100%;
    background: #ffffff;
    padding: 0.187rem 0.427rem 0.427rem; // 14rpx 32rpx 32rpx = 14/75 32/75 32/75
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: space-between;

    &.fixed-bottom {
      position: fixed;
      bottom: 0;
      left: 0;
      z-index: 1000;
      border-top: 0.01rem solid #f0f0f0;
    }

    .left {
      margin-right: 0.853rem; // 64rpx = 64/75
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 1.1rem;

      .img-area {
        position: relative;

        .img {
          width: 0.533rem; // 40rpx = 40/75
          height: 0.533rem; // 40rpx = 40/75
        }

        .message {
          position: absolute;
          top: -0.107rem; // -8rpx = -8/75
          right: -0.453rem; // -34rpx = -34/75
          font-weight: bold;
          font-size: 0.267rem; // 20rpx = 20/75
          line-height: 0.373rem; // 28rpx = 28/75
          color: #ffffff;
          background: #e60003;
          border-radius: 0.24rem; // 18rpx = 18/75
          padding: 0.027rem 0.187rem; // 2rpx 14rpx = 2/75 14/75
          box-sizing: border-box;
          border: 0.027rem solid #ffffff; // 2rpx = 2/75
        }
      }

      .text {
        color: #919499;
        font-size: 0.267rem; // 20rpx = 20/75
      }
    }

    .right {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .btn_2 {
        width: 5.33rem;
        height: 1.12rem; // 84rpx = 84/75
        background: rgba(236, 62, 51, 1);
        border-radius: 0.213rem; // 16rpx = 16/75
        font-weight: 500;
        font-size: 0.373rem; // 28rpx = 28/75
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;

        .img {
          width: 0.427rem; // 32rpx = 32/75
          height: 0.427rem; // 32rpx = 32/75
          margin-right: 0.107rem; // 8rpx = 8/75
        }
      }

      .op08 {
        opacity: 0.8 !important;
      }
    }
  }

  .position-sub-tab-list {
    display: flex;
    align-items: center;
    gap: 0.213rem; // 16rpx = 16/75
    width: fit-content;
    margin-bottom: 0.427rem; // 32rpx = 32/75

    .position-sub-tab-item {
      padding: 0.16rem 0.4rem; // 12rpx 30rpx = 12/75 30/75
      font-size: 0.32rem; // 24rpx = 24/75
      color: #3c3d42;
      background: #ffffff;
      border: 0.013rem solid #ebecf0; // 1rpx = 1/75
      border-radius: 0.107rem; // 8rpx = 8/75
      font-weight: 400;

      &.active {
        color: rgba(230, 0, 3, 0.8);
        background: rgba(230, 0, 3, 0.05);
        border: 0.013rem solid rgba(230, 0, 3, 0.3); // 1rpx = 1/75
      }
    }
  }

  .position-table-content {
    // 表格头部
    .position-table-header {
      display: flex;
      align-items: center;
      background: linear-gradient(180deg, #fef2f2 0%, rgba(254, 242, 242, 0.5) 100%);
      border-radius: 0.107rem; // 8rpx = 8/75
      padding: 0.267rem 0 0.24rem 0; // 20rpx 0 18rpx 0 = 20/75 0 18/75 0

      .header-item {
        font-size: 0.32rem; // 24rpx = 24/75
        color: #93292c;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin: 0 0.107rem; // 8rpx = 8/75

        span {
          line-height: 1.2;
          margin: 0.027rem 0; // 2rpx = 2/75
        }

        // 年份列 - 加宽
        &.header-1 {
          width: 1.6rem; // 120rpx = 120/75
        }

        // 招聘人数列
        &.header-2 {
          width: 1.6rem; // 120rpx = 120/75
        }

        // 报名人数列
        &.header-3 {
          width: 1.6rem; // 120rpx = 120/75
        }

        // 报录比列
        &.header-4 {
          width: 1.333rem; // 100rpx = 100/75
        }

        // 最低进面分列
        &.header-5 {
          width: 1.6rem; // 120rpx = 120/75
        }
      }
    }

    // 表格主体
    .position-table-body {
      .position-table-row {
        display: flex;
        align-items: center;
        padding: 0.453rem 0; // 34rpx = 34/75

        &:nth-of-type(even) {
          background: #f7f8fa;
        }

        .table-item {
          font-size: 0.32rem; // 24rpx = 24/75
          color: #22242e;
          text-align: center;
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 0.107rem; // 8rpx = 8/75

          // 年份列 - 加宽
          &.table-1 {
            width: 1.6rem; // 120rpx = 120/75

            .year-text {
              font-size: 0.32rem; // 24rpx = 24/75
              color: #3c3d42;
              font-weight: 500;
            }
          }

          // 招聘人数列
          &.table-2 {
            width: 1.6rem; // 120rpx = 120/75

            .data-text {
              color: #3c3d42;
              font-size: 0.32rem; // 24rpx = 24/75
            }
          }

          // 报名人数列
          &.table-3 {
            width: 1.6rem; // 120rpx = 120/75

            .data-text {
              color: #3c3d42;
              font-size: 0.32rem; // 24rpx = 24/75
            }
          }

          // 报录比列
          &.table-4 {
            width: 1.333rem; // 100rpx = 100/75

            .data-text {
              color: #3c3d42;
              font-size: 0.32rem; // 24rpx = 24/75
            }
          }

          // 最低进面分列
          &.table-5 {
            width: 1.6rem; // 120rpx = 120/75

            .data-text {
              color: #3c3d42;
              font-size: 0.32rem; // 24rpx = 24/75
            }
          }
        }
      }
    }
  }
}

.orange {
  color: #ff6a4d !important;
}

.red {
  color: #e60003 !important;
}

.mb0 {
  margin-bottom: 0 !important;
}
.flex-center {
  padding: 0 0.64rem;
  box-sizing: border-box;
  .content {
    align-items: center !important;
    justify-content: space-between !important;
    flex-direction: row-reverse !important;
    .num {
      margin-bottom: 0 !important;
    }
  }
}
</style>
