<template>
  <div class="position-section" v-if="tableData.length">
    <div class="position-header">
      <div class="position-title">{{ title }}</div>
      <div
        v-if="showLocationSelector && locationOptions.length > 1"
        class="position-location"
        @click="onLocationClick"
      >
        {{ selectedLocation }}
        <img
          class="location-arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_gray_select.png"
        />
      </div>
    </div>

    <!-- Tab切换 -->
    <div class="position-tab-container" v-if="tabs.length > 1">
      <div class="position-tab-list">
        <div
          v-for="(tab, index) in tabs"
          :key="index"
          class="position-tab-item"
          :class="{ active: currentTab === tab.key }"
          @click="onTabClick(tab.key)"
        >
          {{ tab.name }}
        </div>
      </div>
    </div>

    <!-- 表格内容 -->
    <div class="position-table-content">
      <!-- 表格头部 -->
      <div class="position-table-header">
        <div class="header-item header-rank"></div>
        <div
          v-for="column in columns"
          :key="column.key"
          class="header-item"
          :class="`header-${column.key}`"
        >
          <span v-for="(line, index) in column.title.split(' ')" :key="index">{{ line }}</span>
        </div>
      </div>

      <!-- 表格数据 -->
      <div class="position-table-body">
        <div
          v-for="(item, index) in tableData"
          :key="index"
          class="position-table-row"
        >
          <div class="table-item table-rank">
            <div class="rank-badge" :class="`rank-${index + 1}`">{{ index + 1 }}</div>
          </div>
          <div
            v-for="column in columns"
            :key="column.key"
            class="table-item"
            :class="`table-${column.key}`"
          >
            <span
              :class="column.className || ''"
              @click="column.clickable ? onCellClick(item, column.key) : null"
              :style="{ cursor: column.clickable ? 'pointer' : 'default' }"
            >
              {{ formatCellValue(item[column.dataKey], column.formatter) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 地区选择弹窗 -->
    <van-popup v-model:show="locationVisible" position="bottom">
      <van-picker
        :columns="locationOptions"
        @confirm="onLocationConfirm"
        @cancel="locationVisible = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed } from "vue"

// Props
const props = defineProps({
  title: {
    type: String,
    default: "数据表格"
  },
  tableData: {
    type: Array,
    default: () => []
  },
  columns: {
    type: Array,
    default: () => [
      { key: 'position', title: '职位 名称', dataKey: 'name', clickable: true, className: 'position-title' },
      { key: 'unit', title: '招考 单位', dataKey: 'work_unit', className: 'unit-title' },
      { key: 'score', title: '分数', dataKey: 'score', className: 'score-num' }
    ]
  },
  tabs: {
    type: Array,
    default: () => []
  },
  currentTab: {
    type: String,
    default: ""
  },
  showLocationSelector: {
    type: Boolean,
    default: false
  },
  locationOptions: {
    type: Array,
    default: () => []
  },
  selectedLocation: {
    type: String,
    default: "全部"
  }
})

// Emits
const emit = defineEmits([
  'tabChange',
  'locationChange',
  'cellClick'
])

// 响应式数据
const locationVisible = ref(false)

// 方法
const onTabClick = (tabKey) => {
  emit('tabChange', tabKey)
}

const onLocationClick = () => {
  locationVisible.value = true
}

const onLocationConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    locationVisible.value = false
    emit('locationChange', {
      text: selectedOptions[0].text,
      value: selectedOptions[0].value
    })
  }
}

const onCellClick = (item, columnKey) => {
  emit('cellClick', { item, columnKey })
}

const formatCellValue = (value, formatter) => {
  if (formatter && typeof formatter === 'function') {
    return formatter(value)
  }
  return value || "-"
}
</script>

<style lang="scss" scoped>
.position-section {
  background: #ffffff;
  padding: 0.32rem;

  .position-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.32rem;

    .position-title {
      font-size: 0.427rem;
      font-weight: bold;
      color: #22242e;
    }

    .position-location {
      display: flex;
      align-items: center;
      font-size: 0.347rem;
      color: #666666;
      cursor: pointer;

      .location-arrow {
        width: 0.427rem;
        height: 0.427rem;
        margin-left: 0.107rem;
      }
    }
  }

  .position-tab-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.32rem;

    .position-tab-list {
      display: flex;
      align-items: center;
      border-radius: 0.107rem;
      overflow: hidden;
      width: fit-content;

      .position-tab-item {
        padding: 0.133rem 0.267rem;
        margin-right: 0;
        font-size: 0.32rem;
        color: #666666;
        border: 0.027rem solid #ebecf0;
        border-radius: 0;
        background: #f7f8fa;
        cursor: pointer;

        &.active {
          color: #ffffff;
          background: #e60003;
          border: 0.027rem solid transparent;
        }
      }
    }
  }

  .position-table-content {
    .position-table-header {
      display: flex;
      align-items: center;
      background: linear-gradient(180deg, #fef2f2 0%, rgba(254, 242, 242, 0.5) 100%);
      padding: 0.267rem 0 0.24rem 0;
      border-radius: 0.107rem;

      .header-item {
        font-size: 0.32rem;
        color: #93292c;
        text-align: center;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        margin: 0 0.107rem;

        &.header-rank {
          width: 1.067rem;
          justify-content: center;
          align-items: center;
        }

        &.header-position {
          flex: 1;
          align-items: flex-start;
          padding-left: 0.107rem;
        }

        &.header-unit {
          width: 4.2rem;
          align-items: flex-start;
          padding-left: 0.107rem;
        }

        &.header-score {
          width: 1.6rem;
          justify-content: center;
          align-items: center;
        }

        span {
          line-height: 1.2;
          margin: 0.027rem 0;
        }
      }
    }

    .position-table-body {
      .position-table-row {
        display: flex;
        align-items: center;
        padding: 0.16rem 0;
        border-bottom: 0.013rem solid #f0f0f0;

        .table-item {
          font-size: 0.32rem;
          color: #22242e;
          text-align: left;
          display: flex;
          align-items: center;
          justify-content: flex-start;
          margin: 0 0.107rem;

          &.table-rank {
            width: 1.067rem;
            margin: 0;
            justify-content: center;

            .rank-badge {
              width: 0.64rem;
              height: 0.64rem;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 0.32rem;
              font-weight: bold;
              color: #ffffff;

              &.rank-1 {
                background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
              }

              &.rank-2 {
                background: linear-gradient(135deg, #C0C0C0 0%, #A9A9A9 100%);
              }

              &.rank-3 {
                background: linear-gradient(135deg, #CD7F32 0%, #B8860B 100%);
              }

              &:not(.rank-1):not(.rank-2):not(.rank-3) {
                background: #f0f0f0;
                color: #666666;
              }
            }
          }

          &.table-position {
            flex: 1;
            padding-left: 0.107rem;

            .position-title {
              font-size: 0.293rem;
              color: #448aff;
              line-height: 1.3;
              text-align: left;
              cursor: pointer;
            }
          }

          &.table-unit {
            width: 4.2rem;
            padding-left: 0.107rem;

            .unit-title {
              font-size: 0.293rem;
              color: #666666;
              line-height: 1.3;
              text-align: left;
            }
          }

          &.table-score {
            width: 1.6rem;
            justify-content: center;
            text-align: center;

            .score-num {
              font-size: 0.32rem;
              color: #333333;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
}
</style>
