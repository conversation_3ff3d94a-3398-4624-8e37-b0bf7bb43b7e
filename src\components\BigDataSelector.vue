<template>
  <div class="big-data-selector">
    <!-- 平板布局：三个选择器在同一行 -->
    <div v-if="isTablet" class="select-tablet">
      <div class="select-item" @click="onExamTypeClick">
        <div class="text">{{ selectedExamTypeText }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
      <div class="select-item" @click="onRegionClick">
        <div class="text">{{ selectedRegionText }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
      <div class="select-item" @click="onSpecificExamClick">
        <div class="text">{{ projectName }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
    </div>

    <!-- 手机布局：原有的2+1布局 -->
    <template v-else>
      <div class="select-one">
        <div class="select-item" @click="onExamTypeClick">
          <div class="text">{{ selectedExamTypeText }}</div>
          <img
            class="arrow"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
          />
        </div>
        <div class="select-item" @click="onRegionClick">
          <div class="text">{{ selectedRegionText }}</div>
          <img
            class="arrow"
            src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
          />
        </div>
      </div>

      <div class="select-item w100" @click="onSpecificExamClick">
        <div class="text">{{ projectName }}</div>
        <img
          class="arrow"
          src="https://mpresource-1253756937.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        />
      </div>
    </template>

    <!-- Vant选择器弹窗 -->
    <van-popup v-model:show="examTypeVisible" position="bottom">
      <van-picker
        v-model="selectedExamValues"
        :columns="examTypeOptions"
        @confirm="onExamTypeConfirm"
        @cancel="examTypeVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="regionVisible" position="bottom">
      <van-picker
        v-model="selectedRegionValues"
        title="请选择所属地区"
        :columns="regionOptions"
        @confirm="onRegionConfirm"
        @cancel="regionVisible = false"
      />
    </van-popup>

    <van-popup v-model:show="specificExamVisible" position="bottom">
      <van-picker
        :columns="specificExamOptions"
        @confirm="onSpecificExamConfirm"
        @cancel="specificExamVisible = false"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, computed } from "vue"

// Props
const props = defineProps({
  examTypeOptions: {
    type: Array,
    default: () => []
  },
  regionOptions: {
    type: Array,
    default: () => []
  },
  specificExamOptions: {
    type: Array,
    default: () => []
  },
  selectedExamType: {
    type: String,
    default: "考试类型"
  },
  selectedRegion: {
    type: String,
    default: "所属地区"
  },
  projectName: {
    type: String,
    default: "请选择考试"
  },
  isTablet: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'examTypeChange',
  'regionChange', 
  'specificExamChange'
])

// 响应式数据
const examTypeVisible = ref(false)
const regionVisible = ref(false)
const specificExamVisible = ref(false)
const selectedExamValues = ref([])
const selectedRegionValues = ref([])

// 计算属性
const selectedExamTypeText = computed(() => props.selectedExamType)
const selectedRegionText = computed(() => props.selectedRegion)

// 方法
const onExamTypeClick = () => {
  examTypeVisible.value = true
}

const onRegionClick = () => {
  regionVisible.value = true
}

const onSpecificExamClick = () => {
  specificExamVisible.value = true
}

const onExamTypeConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    examTypeVisible.value = false
    emit('examTypeChange', {
      text: selectedOptions[0].text,
      value: selectedExamValues.value[0]
    })
  }
}

const onRegionConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    regionVisible.value = false
    emit('regionChange', {
      text: selectedOptions[0].text,
      value: selectedRegionValues.value[0]
    })
  }
}

const onSpecificExamConfirm = ({ selectedOptions }) => {
  if (selectedOptions && selectedOptions.length > 0) {
    specificExamVisible.value = false
    emit('specificExamChange', {
      text: selectedOptions[0].text,
      value: selectedOptions[0].value
    })
  }
}
</script>

<style lang="scss" scoped>
.big-data-selector {
  width: 100%;

  // 平板布局的选择器样式
  .select-tablet {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.35rem;
    margin-top: 0.853rem;
    margin-bottom: 0.347rem;

    .select-item {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: rgba(255, 255, 255, 0.7);
      backdrop-filter: blur(0.853rem);
      border-radius: 0.16rem;
      border: 0.013rem solid rgba(255, 255, 255, 0.4);
      padding: 0.24rem 0.267rem 0.24rem 0.32rem;
      box-sizing: border-box;
      cursor: pointer;

      .text {
        color: #22242e;
        font-size: 0.347rem;
      }

      .arrow {
        width: 0.427rem;
        height: 0.427rem;
      }
    }
  }

  .select-one {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-top: 0.853rem;
    margin-bottom: 0.347rem;
  }

  .select-item {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(0.853rem);
    border-radius: 0.16rem;
    border: 0.013rem solid rgba(255, 255, 255, 0.4);
    padding: 0.24rem 0.267rem 0.24rem 0.32rem;
    box-sizing: border-box;
    cursor: pointer;
    
    &:first-of-type {
      margin-right: 0.35rem;
    }

    &.w100 {
      width: 100%;
      margin-top: 0;
    }

    .text {
      color: #22242e;
      font-size: 0.347rem;
    }

    .arrow {
      width: 0.427rem;
      height: 0.427rem;
    }
  }
}
</style>
