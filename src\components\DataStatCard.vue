<template>
  <div class="data-flex-box">
    <div
      v-for="(item, index) in statData"
      :key="index"
      class="flex-item"
    >
      <div class="num" :class="item.colorClass">
        {{ formatNumber(item.value) || "-" }}
      </div>
      <div class="text">{{ item.label }}</div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  statData: {
    type: Array,
    default: () => []
  },
  formatNumber: {
    type: Function,
    default: (num) => {
      if (!num && num !== 0) return "-"
      let newNum = Number(num)
      // 先将数字四舍五入保留一位小数
      const rounded = Math.round(newNum * 10) / 10
      // 检查是否为整数（小数部分为0）
      if (rounded % 1 === 0) {
        return rounded.toString() // 整数直接返回字符串形式
      } else {
        return rounded.toFixed(1) // 非整数保留一位小数
      }
    }
  }
})

// 使用示例：
// const statData = [
//   { value: 85.5, label: '最低进面分数线', colorClass: '' },
//   { value: 95.2, label: '最高进面分数线', colorClass: 'cred' },
//   { value: 90.1, label: '平均进面分数线', colorClass: 'corange' }
// ]
</script>

<style lang="scss" scoped>
.data-flex-box {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.427rem;
  background: rgba(247, 248, 250, 0.5);
  border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;

  .flex-item {
    flex: 1;
    text-align: center;
    padding: 0.32rem;
    position: relative;
    
    &::after {
      content: "";
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 0;
      width: 0.01rem;
      height: 0.64rem;
      background: #ebecf0;
    }

    &:last-child::after {
      display: none;
    }

    .num {
      font-size: 0.53rem;
      font-weight: bold;
      color: #3c3d42;
      margin-bottom: 0.213rem;

      &.cred {
        color: #e60003;
      }

      &.corange {
        color: #ff9500;
      }

      &.cblue {
        color: #448aff;
      }

      &.cgreen {
        color: #4caf50;
      }
    }

    .text {
      font-size: 0.32rem;
      color: #919499;
    }
  }
}
</style>
