<template>
  <div class="region-selector">
    <!-- 选择器触发区域 -->
    <div
      class="select-item"
      :class="{ disabled: !editable }"
      @click="onSelectorClick"
    >
      <div class="select-left">
        <div class="label" v-if="label">
          <span v-for="(char, index) in label" :key="index">{{ char }}</span>
        </div>
        <span class="selected-text">{{ displayText }}</span>
      </div>
      <img
        class="arrow-icon"
        src="https://mpresource-**********.cos.ap-chengdu.myqcloud.com/jbc_jobsearch/big_data/big_data_top_select.png"
        alt=""
      />
    </div>

    <!-- 选择器弹窗 -->
    <van-popup
      v-model:show="showPopup"
      position="bottom"
      @open="onPopupOpen"
    >
      <van-picker
        ref="pickerRef"
        :columns="columns"
        @confirm="onConfirm"
        @cancel="showPopup = false"
        @change="onPickerChange"
        :title="title"
      />
    </van-popup>
  </div>
</template>

<script setup>
import { ref, reactive, watch, nextTick } from "vue"

// Props
const props = defineProps({
  // 选择器配置
  level: {
    type: Number,
    default: 1, // 1=三级联动, 2=市区二级, 3=只选区, 4=不可编辑
  },
  editable: {
    type: Boolean,
    default: true,
  },
  title: {
    type: String,
    default: "请选择地区",
  },
  label: {
    type: String,
    default: "",
  },
  displayText: {
    type: String,
    default: "全部地区",
  },
  selectedValues: {
    type: Array,
    default: () => [],
  },
  // 数据源
  columns: {
    type: Array,
    default: () => [],
  }
})

// Emits
const emit = defineEmits([
  'click',
  'confirm',
  'change'
])

// 响应式数据
const showPopup = ref(false)
const pickerRef = ref(null)

// 方法
const onSelectorClick = () => {
  if (props.editable) {
    showPopup.value = true
    emit('click')
  }
}

const onPopupOpen = () => {
  // 设置选择器的默认选中值
  nextTick(() => {
    const picker = pickerRef.value
    if (picker && props.selectedValues.length > 0) {
      const { level } = props
      const selectedValues = props.selectedValues

      if (level === 1 && selectedValues.length >= 3 && props.columns.length >= 3) {
        // 三级联动，设置省市区
        const provinceIndex = props.columns[0].findIndex(
          (item) => item.value === selectedValues[0]
        )
        const cityIndex = props.columns[1].findIndex(
          (item) => item.value === selectedValues[1]
        )
        const districtIndex = props.columns[2].findIndex(
          (item) => item.value === selectedValues[2]
        )

        if (provinceIndex >= 0) picker.setColumnIndex(0, provinceIndex)
        if (cityIndex >= 0) picker.setColumnIndex(1, cityIndex)
        if (districtIndex >= 0) picker.setColumnIndex(2, districtIndex)
      } else if (level === 2 && selectedValues.length >= 2 && props.columns.length >= 2) {
        // 市区二级，设置市区
        const cityIndex = props.columns[0].findIndex(
          (item) => item.value === selectedValues[1]
        )
        const districtIndex = props.columns[1].findIndex(
          (item) => item.value === selectedValues[2]
        )

        if (cityIndex >= 0) picker.setColumnIndex(0, cityIndex)
        if (districtIndex >= 0) picker.setColumnIndex(1, districtIndex)
      } else if (level === 3 && selectedValues.length >= 3 && props.columns.length >= 1) {
        // 只选区县
        const districtIndex = props.columns[0].findIndex(
          (item) => item.value === selectedValues[2]
        )
        if (districtIndex >= 0) picker.setColumnIndex(0, districtIndex)
      }
    }
  })
}

const onConfirm = ({ selectedOptions }) => {
  showPopup.value = false
  
  // 根据level处理不同的确认逻辑
  const { level } = props
  let result = {}

  if (level === 3 && selectedOptions.length > 0) {
    // 只选择区县
    const district = selectedOptions[0]
    result = {
      text: district.text,
      values: [null, null, district.value],
      options: [null, null, district]
    }
  } else if (level === 2 && selectedOptions.length >= 2) {
    // 选择市区
    const city = selectedOptions[0]
    const district = selectedOptions[1]
    result = {
      text: `${city.text}-${district.text}`,
      values: [null, city.value, district.value],
      options: [null, city, district]
    }
  } else if (level === 1 && selectedOptions.length >= 3) {
    // 三级联动
    const province = selectedOptions[0]
    const city = selectedOptions[1]
    const district = selectedOptions[2]
    result = {
      text: `${province.text}-${city.text}-${district.text}`,
      values: [province.value, city.value, district.value],
      options: [province, city, district]
    }
  }

  emit('confirm', result)
}

const onPickerChange = (selectedValues, columnIndex) => {
  emit('change', { selectedValues, columnIndex })
}
</script>

<style lang="scss" scoped>
.region-selector {
  .select-item {
    background: #ffffff;
    border-radius: 0.213rem;
    border: 0.013rem solid #ebecf0;
    margin-bottom: 0.32rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.347rem 0.32rem 0.347rem 0.427rem;
    box-sizing: border-box;
    cursor: pointer;

    &:last-of-type {
      margin-bottom: 0;
    }

    // 不可编辑状态样式
    &.disabled {
      background: #f5f5f5 !important;
      border-color: #e0e0e0 !important;
      opacity: 0.6;
      pointer-events: none;

      .select-left {
        color: #999999 !important;

        .label {
          color: #999999 !important;
        }

        .selected-text {
          color: #999999 !important;
        }
      }

      .arrow-icon {
        opacity: 0.5;
      }
    }

    .select-left {
      display: flex;
      align-items: center;
      color: #3c3d42;
      font-size: 0.347rem;
      flex: 1;

      .label {
        width: 1.85rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
      }

      .selected-text {
        flex: 1;
        margin-left: 0;
      }
    }

    .arrow-icon {
      width: 0.43rem;
      height: 0.43rem;
    }
  }
}
</style>
