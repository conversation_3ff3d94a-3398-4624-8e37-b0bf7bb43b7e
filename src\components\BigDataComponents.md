# BigData 通用组件使用指南

## 组件概述

基于 `interviewScore.vue` 页面分析，我们封装了以下通用组件：

### 1. BigDataSelector - 三级联动选择器
**功能**：考试类型-地区-具体考试的三级选择器
**适用场景**：bigData.vue, interviewScore.vue 等页面

### 2. BigDataChart - 可配置图表组件  
**功能**：支持不同数据类型的柱状图展示
**适用场景**：各地域数据展示

### 3. BigDataTable - 排行榜表格组件
**功能**：支持排序、地区筛选的数据表格
**适用场景**：职位排行榜展示

### 4. RegionSelector - 地区选择器
**功能**：支持1-4级的地区选择器
**适用场景**：地区筛选功能

### 5. DataStatCard - 数据统计卡片
**功能**：数据指标展示卡片
**适用场景**：关键数据展示

### 6. JobSearchForm - 岗位查询表单
**功能**：集成地区、单位、职位的查询表单
**适用场景**：岗位搜索功能

### 7. SearchResultTable - 搜索结果表格
**功能**：搜索结果的表格展示
**适用场景**：查询结果展示

## 使用示例

### 在 interviewScore.vue 中的使用

```vue
<template>
  <div class="interview-score">
    <!-- 顶部选择器 -->
    <div class="top-area">
      <BigDataSelector
        :exam-type-options="examTypeOptions"
        :region-options="regionOptions"
        :specific-exam-options="specificExamOptions"
        :selected-exam-type="selectedExamType"
        :selected-region="selectedRegion"
        :project-name="projectName"
        :is-tablet="isTablet"
        @exam-type-change="onExamTypeChange"
        @region-change="onRegionChange"
        @specific-exam-change="onSpecificExamChange"
      />
    </div>

    <!-- 数据统计卡片 -->
    <DataStatCard
      :stat-data="statCardData"
      :format-number="formatNumber"
    />

    <!-- 图表区域 -->
    <BigDataChart
      title="各地域进面数据"
      :chart-data="detailData.region_min_score_list"
      data-key="min_min_score"
      name-key="area_name"
      :tooltip-formatter="chartTooltipFormatter"
      :format-number="formatNumber"
    />

    <!-- 职位排行榜 -->
    <BigDataTable
      title="职位进面分"
      :table-data="currentTableData"
      :columns="tableColumns"
      :tabs="tableTabs"
      :current-tab="currentMainTab"
      :show-location-selector="true"
      :location-options="positionLocationOptions"
      :selected-location="regionName"
      @tab-change="onMainTabClick"
      @location-change="onPositionLocationChange"
      @cell-click="onTableCellClick"
    />

    <!-- 岗位查询表单 -->
    <JobSearchForm
      title="岗位进面分数查询"
      :region-config="searchRegionConfig"
      :unit-options="searchUnitOptions"
      :selected-unit-text="selectedSearchUnitText"
      :position-input="positionInput"
      @region-confirm="onSearchRegionConfirm"
      @unit-confirm="onSearchUnitConfirm"
      @search="onSearchClick"
      @clear="onClearSearch"
      @position-input-change="onPositionInputChange"
    />

    <!-- 搜索结果表格 -->
    <SearchResultTable
      :table-data="jobList"
      :columns="searchResultColumns"
      :format-number="formatNumber"
      @cell-click="onSearchResultCellClick"
    />
  </div>
</template>

<script setup>
import BigDataSelector from "@/components/BigDataSelector.vue"
import BigDataChart from "@/components/BigDataChart.vue"
import BigDataTable from "@/components/BigDataTable.vue"
import DataStatCard from "@/components/DataStatCard.vue"
import JobSearchForm from "@/components/JobSearchForm.vue"
import SearchResultTable from "@/components/SearchResultTable.vue"

// 数据配置
const statCardData = computed(() => [
  { 
    value: detailData.value.min_score_info?.min_min_score, 
    label: '最低进面分数线', 
    colorClass: '' 
  },
  { 
    value: detailData.value.min_score_info?.max_min_score, 
    label: '最高进面分数线', 
    colorClass: 'cred' 
  },
  { 
    value: detailData.value.min_score_info?.avg_min_score, 
    label: '平均进面分数线', 
    colorClass: 'corange' 
  }
])

const tableColumns = [
  { 
    key: 'position', 
    title: '职位 名称', 
    dataKey: 'name', 
    clickable: true, 
    className: 'position-title' 
  },
  { 
    key: 'unit', 
    title: '招考 单位', 
    dataKey: 'work_unit', 
    className: 'unit-title' 
  },
  { 
    key: 'score', 
    title: '最低进 面分', 
    dataKey: 'min_score', 
    className: 'recruit-num',
    formatter: formatNumber
  }
]

const tableTabs = [
  { key: 'desc', name: '高分TOP10' },
  { key: 'asc', name: '低分TOP10' }
]

const searchResultColumns = [
  { 
    key: 'position', 
    title: '职位名称', 
    dataKey: 'name', 
    clickable: true, 
    className: 'position-name' 
  },
  { 
    key: 'unit', 
    title: '工作单位', 
    dataKey: 'work_unit', 
    className: 'unit-name' 
  },
  { 
    key: 'score', 
    title: '最低进面分数', 
    dataKey: 'min_score', 
    className: 'score-value',
    formatter: formatNumber
  }
]

// 事件处理
const onExamTypeChange = ({ text, value }) => {
  selectedExamType.value = text
  selectedExamTypeKey.value = value
  // 其他逻辑...
}

const chartTooltipFormatter = (params) => {
  const data = detailData.value.region_min_score_list[params.dataIndex + 1]
  return [
    `<div style="font-size: 0.32rem; color: #3C3D42; margin-bottom: 8px;">${data.area_name}</div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">最低进面分</span><span style="color: #3C3D42; font-weight: 400;">${formatNumber(data.min_min_score) || 0}</span></div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">最高进面分</span><span style="color: #3C3D42; font-weight: 400;">${formatNumber(data.max_min_score) || 0}</span></div>`,
    `<div style="font-size: 0.29rem; color: #919499;"><span style="width: 1.2rem;margin-right: 0.2rem;">平均进面分</span><span style="color: #3C3D42; font-weight: 400;">${formatNumber(data.avg_min_score) || 0}</span></div>`,
  ].join("")
}
</script>
```

## 组件优势

1. **高复用性**：组件可在多个页面间复用
2. **可配置性**：通过 props 配置不同的展示效果
3. **统一样式**：保持设计系统的一致性
4. **易维护性**：集中管理相似功能的代码
5. **类型安全**：使用 TypeScript 提供更好的开发体验

## 迁移建议

1. **逐步迁移**：先在新功能中使用组件，再逐步重构现有页面
2. **保持兼容**：确保组件 API 向后兼容
3. **文档完善**：为每个组件编写详细的使用文档
4. **测试覆盖**：为组件编写单元测试和集成测试
